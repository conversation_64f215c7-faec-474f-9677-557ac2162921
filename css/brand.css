/* General Settings */
/* General Settings - Fonts */
@import url('https://fonts.googleapis.com/css?family=Open+Sans:300,400');
/* Global - Resets */
* {
-webkit-box-sizing:border-box;
-moz-box-sizing:border-box;
box-sizing:border-box;
}
html,body {
height:100%;
margin:0;
font:lighter 16px 'Open Sans','Segoe UI','Gill Sans','Gill Sans MT',Gill<PERSON><PERSON>,'Trebuchet MS',Arial,sans-serif;
color:#666;
-webkit-text-size-adjust:100%;
-ms-text-size-adjust:100%;
}
article,aside,details,figcaption,figure,footer,header,hgroup,nav,section,summary {
display:block;
}
figure {
margin:0;
}
blockquote {
margin:1em 40px;
}
p,pre {
margin:1em 0;
}
p,.smallest_text {
font-size:1.1em;
}
sub {
font-size:1em;
line-height:1em;
}
pre {
padding:2em;
border-radius:5px;
box-shadow:inset 0 0 5em rgba(0,0,0,.15);
white-space:pre;
white-space:pre-wrap;
word-wrap:break-word;
}
/* Global - Colors */
h1,h2,h3,h4,h5,h6,dt,.notice {
color:#3575B9;
}
.a,a {
color:#0D53B4;
}
.identity {
text-transform:none;
font-size:1em !important;
}
.blueblock {
border-style:solid;
border-width: 1px 0;
border-color:#3575d3;
background-color:#edf4f9;
}
.ftc_primary {
color:#0D53B4;
}
.ftc_secondary {
color:#3575D3;
}
/* Global - Font Styles */
.bold,.bt,b,strong,acronym,.cite_source,.row_header,.col_header,.alert,.warning,.success,.attention,.accent,.super_error,.super_success {
font-weight:600;
}
.as_is,.no_tt,abbr {
text-transform:none;
}
.italic,.it {
font-style:italic;
}
pre,small,.small {
font-size:.9em;
}
ins {
text-decoration: none;
}
.uc,.uppercase {
text-transform:uppercase;
}
.lc,.lowercase {
text-transform:lowercase;
}
.underline {
text-decoration:underline;
}
/* Global - Reading */
.readable {
font-family:monospace;
}
.ellipsis {
overflow:hidden;
text-overflow:ellipsis;
white-space:nowrap;
}
/* Global - Headings */
h1,h2,h3,h4,h5,h6,dt,.subheading {
margin:1em 0;
font-weight:lighter;
}
h1, .heading {
font-size:3em;
line-height:1em;
}
h2 {
font-size:2.5em;
line-height:1em;
}
h3,.subheading {
font-size:2em;
}
h4 {
font-size:1.625em;
}
h5,h6,dt {
font-size:1.2em;
}
/* Global - Links */
a,.a {
text-decoration:none;
cursor:pointer;
}
a:hover,.a:hover {
text-decoration:underline;
}
a:focus,.a:focus,button:focus,input:focus,.btn:focus {
outline:none;
}
a:active,a:hover,.a:active,.a:hover {
outline:none;
}
.sr, /* so screen reader can read labels */
.p_hide {
display:none;
}
/* Global - IE */
.lt_ie9 #footer {
margin:-15em auto 0em !important;
}
.lt_ie9 #container {
padding:4em 0 15em !important;
}
.lt_ie9 .visible_flying_footer {
background:#fff;
}
.ie_warn {
background:#333;
display:block;
height:4em;
text-align:center;
line-height:4em;
color:#FFF;
position:fixed;
right:0;
bottom:0;
left:0;
z-index:9999;
}
/* Global - Images */
img {
border:0;
-ms-interpolation-mode:bicubic;
}
/* Disclaimers */
.disclaimer {
font-size: .8em;
width: 75em;
margin: 0 auto 50px;
padding: 0 0 1em;
}

section.disclaimer {
 margin:0;
}

/* -------------------------------------------------------------------------- */
/* Utilities */
/* -------------------------------------------------------------------------- */

.pull-left {
float: left;
}
.pull-right {
float: right;
}

/* hover call-to-action anchors */
.hover_cta {
color:#666;
}
.hover_cta:hover {
text-decoration:none;
}

/* Global - Buttons */
/* Buttons - General */
.btn,.btn_primary,.btn_secondary,.btn_dark,.btn_icon,.btn_hairline,.btn_hairline_inverse {
font:lighter 16px 'Open Sans','Segoe UI','Gill Sans','Gill Sans MT',GillSans,'Trebuchet MS',Arial,sans-serif;
display:inline-block;
*display:inline;
zoom:1;
width:auto;
padding:.35em 2em;
border-radius:5px;
overflow:visible;
vertical-align:baseline;
text-align:center;
text-decoration:none;
text-transform:lowercase;
font-weight:lighter;
line-height:1.29em;
cursor:pointer;
position:relative;
*z-index:1;
}
.btn img,.btn_primary img,.btn_secondary img,.btn_dark img,.btn_icon img,.btn_hairline img,.btn_hairline_inverse img {
display:inline-block;
vertical-align:middle;
}
.btn,button,.btn_icon {
background:#EDEDED;
background-clip:padding-box;
color:#555;
border:#cfcfcf 1px solid;
}
.btn:hover,button:hover,.btn_icon:hover,a:hover .btn {
background:#EFEFEF;
text-decoration:none;
}
.btn_primary,.btn_secondary,.hovercta:hover .btn {
color:#FFF;
}
.btn_primary:hover,.btn_secondary:hover,.btn_dark:hover,.btn_icon:hover,.btn_hairline:hover,.btn_hairline_inverse:hover {
text-decoration:none;
}
/* Buttons - Primary */
.btn_primary {
background: #62bc33;
background: -moz-linear-gradient(left, #62bc33 0%, #8bd331 100%);
background: -webkit-gradient(linear, left top, right top, color-stop(0%,#62bc33), color-stop(100%,#8bd331));
background: -webkit-linear-gradient(left, #62bc33 0%,#8bd331 100%);
background: -o-linear-gradient(left, #62bc33 0%,#8bd331 100%);
background: -ms-linear-gradient(left, #62bc33 0%,#8bd331 100%);
background: linear-gradient(to right, #62bc33 0%,#8bd331 100%);
border:none;
}
.btn_primary.big,
.btn_secondary.big {
padding:.25em 4.7em .25em 1em;
font-size:1.8em;
line-height:1.5em;
text-align:left;
-webkit-box-shadow: 0 4px 15px rgba(0,0,0,.3);
box-shadow: 0 4px 15px rgba(0,0,0,.3);
}
.btn_primary.medium, .btn_dark.medium {
padding: .27em 1em;
font-size: 1.4em;
line-height: 1.4em;
}
.btn_primary:hover,.btn_sticker:hover,a:hover .btn_sticker,a:hover .btn_primary {
background:#8bd331;
color:#FFF;
}
.btn_primary:active,.btn_sticker:active,a:active .btn_sticker,a:active .btn_primary {
background:#62bc33;
color:#FFF;
}
/* Buttons - Secondary */
.btn_secondary,button.btn_secondary,input.btn_secondary,.hovercta:hover .btn {
background:#3676B8;
border:1px solid #3575B9;
}
.btn_secondary:hover,button.btn_secondary:hover,input.btn_secondary:hover,.hovercta:hover .btn:hover,a:hover .btn_secondary {
background:#3676B8;
}
.btn_secondary:active,button.btn_secondary:active,input.btn_secondary:active,.hovercta:hover .btn:active,a:active .btn_secondary {
background:#0D53B4;
}
/* Buttons - Hairline */
.btn_hairline,.btn_hairline:disabled,.btn_hairline.btn_disabled {
background:transparent;
padding:.3em 2.8em .3em 1.1em;
border:1px solid #3676B8;
border-radius:5px;
text-align:left;
color:#3575B9;
position:relative;
}
.btn_hairline:after {
content:'';
background:transparent url(/media/shared/general/icons/arrow_r.svg) no-repeat right center;
background-size:39px;
display:block;
width:26px;
height:26px;
position:absolute;
top:2px;
right:0;
}
.lt_ie9 .btn_hairline:after {
content:url(/media/shared/general/icons/arrow_r.png);
top:.3em;
}
.btn_primary.big:after,
.btn_secondary.big:after {
content:'';
background:transparent url(/media/shared/general/icons/arrow_r_white.svg) no-repeat right center;
background-size:65px;
display:block;
width:45px;
height:45px;
position:absolute;
top:6px;
right:0;
}
.lt_ie9 .btn_primary.big:after,
.lt_ie9 .btn_secondary.big:after {
content:url(/media/shared/general/icons/arrow_r_white.png);
top:.3em;
}
.hovercta:hover .btn_hairline,
.text-light .addon-info:hover .btn_hairline,
.btn_hairline.active,
.btn_hairline.inverse.active,
.btn_hairline:hover,
.btn_hairline.inverse:hover,
a:hover .btn_hairline {
background:#3676B8;
border-color:#3676B8;
color:#FFF;
}
.btn_hairline:active,
.btn_hairline.inverse:active,
a:active .btn_hairline {
background:#2356A6;
border-color:#2356A6;
}
.btn_hairline.inverse {
border-color:#FFF;
color:#FFF;
}
.addon-info:hover .btn_hairline:after,
.btn_hairline:active:after,
.btn_hairline.inverse:active:after,
.btn_hairline.active:after,
.btn_hairline.inverse.active:after,
.btn_hairline:hover:after,
.btn_hairline.inverse:hover:after,
.btn_hairline.inverse:after,
a:active .btn_hairline:after,
a:hover .btn_hairline:after {
background-image:url(/media/shared/general/icons/arrow_r_white.svg);
}
.lt_ie9 .btn_hairline:active:after,.lt_ie9 .btn_hairline.inverse:active:after,.lt_ie9 .btn_hairline.active:after,.lt_ie9 .btn_hairline.inverse.active:after,.lt_ie9 .btn_hairline:hover:after,.lt_ie9 .btn_hairline.inverse:hover:after,.lt_ie9 .btn_hairline.inverse:after {
content:url(/media/shared/general/icons/arrow_r_white.png);
}
.lt_ie9 .btn_hairline:active:after,.lt_ie9 .btn_hairline.inverse:active:after,.lt_ie9 .btn_hairline.active:after,.lt_ie9 .btn_hairline.inverse.active:after,.lt_ie9 .btn_hairline:hover:after,.lt_ie9 .btn_hairline.inverse:hover:after,.lt_ie9 .btn_hairline.inverse:after {
content:url(/media/shared/general/icons/arrow_r_white.png);
}
.btn:disabled,.btn.btn_disabled,.btn_primary:disabled,.btn_primary.btn_disabled,.btn_secondary:disabled,.btn_secondary.btn_disabled {
background-color: #9A9A9A;
background-image: none;
border: 1px solid #9A9A9A;
color: #FFF;
cursor: default;
}
.btn:disabled:hover,.btn.btn_disabled:hover,.btn_primary:disabled:hover,.btn_primary.btn_disabled:hover,.btn_secondary:disabled:hover,.btn_secondary.btn_disabled:hover {
background-color: #9A9A9A;
color: #FFF;
}
.btn_hairline:disabled:hover,.btn_hairline.btn_disabled:hover {
color: #A9A9A9;
}
.btn_hairline:disabled,.btn_hairline.btn_disabled,.btn_hairline:disabled:hover,.btn_hairline.btn_disabled:hover {
background-color: transparent;
border: 1px solid #A9A9A9;
color: #A9A9A9;
}
/* Buttons - Prereq */
.add_to_cart,.prereq {
float:right;
}
.prereq.btn_primary,.btn_primary.add_to_cart {
font-size:1em;
}
.add_to_cart {
padding-left:2.3em;
}
.prereq {
margin-right:0;
position:relative;
left:1.5em;
z-index:99;
}
.prereq em {
margin-left:.4em;
}
.prereq.btn_primary em {
font-style:normal;
}
.btn_primary .peek_toggle {
color:#FFF;
}
.prereq .peek_toggle {
text-decoration:underline;
}
.add_to_cart.disabled,.prereq_accepted {
background:#FFF;
border-color:#7DAB2E;
color:#3676B8;
}
/* Shadows on content */
.shadow_bottom,.shadow_beneath,.shadow_top {
position:relative;
}
.shadow_bottom:after {
content:'';
background:-webkit-gradient(linear,left bottom,left top,color-stop(0%,rgba(0,0,0,.5)),color-stop(42%,rgba(0,0,0,.2)),color-stop(100%,rgba(0,0,0,0)));
background:-webkit-linear-gradient(bottom,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-moz-linear-gradient(bottom,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-ms-linear-gradient(bottom,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-o-linear-gradient(bottom,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:linear-gradient(to top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
display:block;
width:100%;
height:.2em;
position:absolute;
}
.shadow_beneath:after {
content:'';
background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,rgba(0,0,0,.5)),color-stop(42%,rgba(0,0,0,.2)),color-stop(100%,rgba(0,0,0,0)));
background:-webkit-linear-gradient(top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-moz-linear-gradient(top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-ms-linear-gradient(top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-o-linear-gradient(top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:linear-gradient(to bottom,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
display:block;
width:100%;
height:1em;
position:absolute;
}
.shadow_top:before {
content:'';
background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,rgba(0,0,0,.5)),color-stop(42%,rgba(0,0,0,.2)),color-stop(100%,rgba(0,0,0,0)));
background:-webkit-linear-gradient(top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-moz-linear-gradient(top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-ms-linear-gradient(top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:-o-linear-gradient(top,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
background:linear-gradient(to bottom,rgba(0,0,0,.5) 0%,rgba(0,0,0,.2) 42%,rgba(0,0,0,0));
display:block;
width:100%;
height:.6em;
position:absolute;
top:0;
}
/* Slideshow */
.slideshow {
display:block;
box-shadow:inset 0 0 2em rgba(0,0,0,.2);
text-align:center;
position:relative;
}
.button_wrapper {
display:block;
max-width:60em;
margin:0 auto;
position:relative;
}
.btn_slide {
background:transparent;
display:inline-block;
width:3.25em;
height:3.25em;
border-color:rgba(255,255,255,.6);
border-radius:1.8em;
overflow:hidden;
font-weight:bold;
font-size:1em;
line-height:3em;
color:rgba(255,255,255,.6);
cursor:pointer;
position:absolute;
top:40%;
}
.btn_slide:hover {
background:transparent;
border-color:#FFF;
color:#FFF;
}
.btn_slide:active {
background:#3575B9;
}
.ie8 .btn_slide,.ie8 .btn_slide:hover,.ie8 .btn_slide:active {
background:transparent;
border:0;
overflow:visible;
color:#FFF;
}
.btn_slide:after {
content:'\003e';
display:inline-block;
margin-left:.5em;
font-weight:normal;
font-size:.6em;
-webkit-transform:scale(2.8,6);
-moz-transform:scale(2.8,6);
-ms-transform:scale(2.8,6);
-o-transform:scale(2.8,6);
transform:scale(2.8,6);
position:relative;
top:.1em;
}
.btn_slide.btn_left:after {
content:'\003c';
margin-left:-0.4em;
}
.ie8 .btn_slide:after {
margin:0;
font-size:5em;
top:-.1em;
}
.ie8 .btn_slide.btn_left:after {
margin-left:-.2em;
}
.ie9 .btn_slide:after {
top:-.8em;
}
.slideshow .btn_left {
left:1em;
}
.slideshow .btn_right {
right:1em;
}
.slideshow .pagers {
display:inline-block;
min-width:10em;
margin:0 0 0 -2%;
padding:0;
position:absolute;
bottom:.5em;
left:50%;
}
.pagers li {
margin:0;
padding:0;
list-style:none;
}
.pagers li a {
background:rgba(255,255,255,0.4);
display:inline;
float:left;
width:10px;
height:10px;
margin-right:.5em;
padding:0;
border-radius:5px;
box-shadow:1px 1px 2px rgba(0,0,0,.3);
}
.pagers li a:hover {
background:#FFF;
}
.pagers li a:active {
background:#3575B9;
}
a,button,input,*:active,*:focus,*:selected,*::-moz-focus-inner {
outline:0;
outline-width:0;
outline-style:none;
}
/* User Messages */
.tos_list {
background:#f4f4f4;
padding:2em;
-webkit-box-shadow:inset 0em .5em 1em rgba(0,0,0,.1);
box-shadow:inset 0em .5em 1em rgba(0,0,0,.1);
margin-bottom: 4em;
}
.tos_list ul,
.tos_list li {
margin:0;
padding:0;
list-style:none;
}
.tos_list ul {
margin:0 auto;
width:50em;
}
.tos_list li {
display:block;
float:left;
width:16em;
margin-right:.5em;
}
.tos_list a {
overflow:hidden;
white-space:nowrap;
text-overflow:ellipsis;
padding:.3em;
color:#575757;
display:block;
text-transform:lowercase;
}
.tos_list a.active,
.tos_list a.active:hover {
color:#3676B8;
font-weight:700;
}
.terms_conditions h1,
.terms h2 {
text-align:center;
}
.tos {
margin-bottom:2em;
}
.tos h5 a {
background:transparent url('/media/shared/general/icons/arrow_r.svg') no-repeat right center;
background-size:1.6em;
padding-right:1.3em;
}
.unlimited-img {
margin: 0 0 2em;
}
.unlimited-img img {
width: 696px;
height: 438px;
}
/* User Messages */
.slidestitial {
background:#3575C5;
min-width:25em;
max-width:45em;
max-height:40em;
padding:0;
padding:3em 3em 3em 4em;
border-radius:5px;
color:#FFF;
-webkit-transition:all .5s ease-out;
-moz-transition:all .5s ease-out;
-o-transition:all .5s ease-out;
transition:all .5s ease-out;
position:fixed;
top:25%;
left:-90%;
z-index:10002;
}
.slidestitial.active {
left: -.3%;
}
.slidestitial-close {
cursor:pointer;
position:absolute;
top:1em;
right:1.2em;
}
.no_js .slidestitial {
display:block;
width:100%;
margin:1em 0 2em;
position:relative;
top:0;
left:0;
}
.lt_ie9 aside.addon,
.no_js .slidestitial-close {display:none;}
.slidestitial-fade {
background:rgba(255,255,255,0.8);
display:block;
width:100%;
height:100%;
cursor:pointer;
position:fixed;
top:0;
left:0;
z-index:10001;
}
.peek {
display:block;
clear:both;
width:100%;
max-height:0;
margin:1em 0;
padding:0 2em;
border:none;
-webkit-box-shadow:none;
box-shadow:none;
overflow-y:scroll;
-webkit-transition:all .3s ease-out;
-moz-transition:all .3s ease-out;
-o-transition:all .3s ease-out;
transition:all .3s ease-out;
position:relative;
}
.peek.active {
max-height:20em;
margin:5em 0;
padding:2em;
border-top:solid 1px #E7E7E7;
border-bottom:solid 1px #E7E7E7;
}
.peek.active:after,.peek.active:before {
content:'';
display:block;
width:56em;
height:10px;
margin:0 auto;
border-radius:20em;
-webkit-box-shadow:0 0 10em #BBB;
box-shadow:0 0 10em #BBB;
position:absolute;
z-index:90;
}
.peek.active:before {
top:-10px;
}
.peek.active:after {
bottom:-10px;
}
.no_js .peek {
display:block;
}
/* super-stitial */
#superstitial {
width:100%;
margin:0;
padding:0;
position:fixed;
top:0;
left:0;
z-index:9999;
}
.super {
height:0;
margin:0;
padding:0 2em;
-webkit-box-shadow:0 4px 10px rgba(0,0,0,.3);
box-shadow:0 4px 10px rgba(0,0,0,.3);
overflow:hidden;
text-align:center;
line-height:1.3em;
-webkit-transition:all .3s ease-out;
-moz-transition:all .3s ease-out;
-o-transition:all .3s ease-out;
transition:all .3s ease-out;
}
#superstitial .active {
display:block;
height:auto;
padding:1em 2em;
}
.super_success {
background:#AAD868;
color:#2F440A;
}
.super_error {
background:#FAFA8A;
color:#000;
}
/* Global - Forms */
form {
margin:0;
}
button,input,select,textarea {
margin:0;
border-radius:5px;
vertical-align:baseline;
color:#666;
}
button,input {
line-height:normal;
}
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
-webkit-appearance:button;
-moz-appearance:button;
}
button:-moz-focus-inner,input:-moz-focus-inner {
padding:0;
border:0;
}
input[type="search"] {
-webkit-appearance:textfield;
-moz-appearance:textfield;
}
input[type="search"]:-webkit-search-cancel-button,
input[type="search"]:-webkit-search-decoration {
-webkit-appearance:none;
-moz-appearance:none;
}
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="text"] {
padding:.3em .4em;
border:1px solid #3575B9;
outline:0;
overflow:hidden;
text-overflow:ellipsis;
white-space:nowrap;
vertical-align:top;
font-size:1em;
line-height:1.25em;
color: #3575B9;
-webkit-transition:all .175s ease-in 0;
-moz-transition:all .175s ease-in 0;
-o-transition:all .175s ease-in 0;
transition:all .175s ease-in 0;
*height:auto;
}
input[type="email"]:focus,input[type="password"]:focus,input[type="search"]:focus,input[type="text"]:focus {
-webkit-transition:all .175s ease-in 0;
-moz-transition:all .175s ease-in 0;
-o-transition:all .175s ease-in 0;
transition:all .175s ease-in 0;
}
label[for] {
cursor:pointer;
}
label {
font-size:1.2em;
color:#3575B9;
}
fieldset {
margin:0 2px;
padding:.35em .625em .75em;
border:1px solid #C0C0C0;
}
input[type="checkbox"],input[type="radio"] {
padding:0;
*height:13px;
*width:13px;
}
select,textarea {
border:1px solid #3575B9;
}
textarea {
padding:.3em .4em;
overflow:auto;
vertical-align:top;
font-size:1em;
color:#4D4D4D;
}
textarea[disabled] {
background:#E1E1E1 !important;
border:1px solid #A7A9AC !important;
text-shadow:none !important;
cursor:default;
}
.dropdown ul,
.file:focus,
textarea:focus,
select:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="email"]:focus {
border-color:#3575B9;
outline:0;
}
.placeholder {
text-transform:lowercase;
color: #3575B9 !important;
}
::-webkit-input-placeholder {
text-transform:lowercase;
color: #3575B9;
opacity:1;
}
:-ms-input-placeholder {
text-transform:lowercase;
color: #3575B9;
opacity:1;
}
::-moz-placeholder {
text-transform:lowercase;
color: #3575B9;
opacity:1;
}
:-moz-placeholder {
text-transform:lowercase;
color: #3575B9;
opacity:1;
}
.form label {
display:inline-block;
*display:inline;
zoom:1;
width:11em;
padding:.2em 1em 0 0;
text-align:right;
}
.form input[type="email"],.form input[type="password"],.form input[type="search"],.form input[type="text"] {
vertical-align:top;
}
.form select {
vertical-align:middle;
}
/* Global - Lists */
ol,ul {
margin:1em 0;
padding:0 0 0 40px;
}
nav ol,nav ul {
margin:0;
padding:0;
list-style:none;
list-style-image:none;
}
.bullet_list {
list-style: none;
padding:0px;
}
.bullet_list li {
position:relative;
}
.bullet_list li:before {
content: '\2022';
margin: 0 .2em 0 0;
position: absolute;
left:.3em;
}
dl {
display:inline-block;
padding:0 0 0 2.5em;
}
dd,li {
margin:0 0 .2em 0;
}
.bulletless {
margin:0;
padding:0;
}
.bulletless li {
display:block;
clear:both;
list-style-type:none;
}
/* Global - Tables ( invoice,email,etc) */
/* Notes on the table markup are in the styleguide */
table {
border-spacing:0;
border-collapse:collapse;
}
table,.table,.table li {
margin:0;
padding:0;
list-style:none;
}
table th,table td {
padding:.3em .35em;
}
/* make the ul list header match the columns below. */
table thead {
display:table-header-group;
}
.odd,.odd td {
background-color:#E0E0E0;
}
.even,.even td {
background-color:#F8F8F8;
}
table thead th,.table .row_header {
background:none;
font-style:italic;
}
.table .col,tr td,tr th {
padding:.5em 1em;
border-right:1px solid #BBB;
}
.table .col {
display:inline;
float:left;
}
table tr:hover td,table tr:hover th,.table li:hover,.table li:hover .col_header {
background:#80A5DB;
color:#FFF;
}
table tr:hover th:hover,table tr:hover th,table tr td:hover,.table li:hover .col:hover,.table li:hover .col_header {
background:#3575B9;
}
.table .row_header .col,thead tr th {
background:none;
border-right:1px solid #EFEFEF;
}
.table .col.last {
border-right:none;
}
table th,table td {
padding:.5em 1em;
border-right:1px solid #BBB;
text-align:left;
}
table th,.table .col_header {
background:#CCC;
border-right:2px solid #999;
}
/* Global - Figures */
.figure_right,.img_right {
float:right;
margin:0 0 2em 1em;
}
.figure_left,.img_left {
float:left;
margin:0 2em 1em 0;
}
.img_mid_align {
padding-top:1em;
}
figure {
background:#EFEFEF;
padding:1em;
border-radius:5px;
position:relative;
}
figure h4 {
margin-top:0;
text-align:center;
}
figcaption {
text-align:center;
line-height:2em;
}
/* Global - Display Classes */
.inline {
display:inline;
}
.ib,.inline_block {
display:inline-block;
*display:inline;
zoom:1;
}
.hidden {
display:none !important;
}
/* Global - Clear Fixes */
.clear:before,.clear:after {
content:'';
display:table;
visibility:hidden;
}
.clear:after {
clear:both;
}
/* Browser Warnings - JavaScript Disabled */
.js_on #js_required {
display:none;
}
/* General Settings - Boxes */
.accent,.attention,.box,.callout,.critical,.shaded,.alert,.warning {
margin:1.5em auto;
padding:1em 2em;
}
.accent:first-child,.attention:first-child,.box:first-child,.critical:first-child,.shaded:first-child {
margin-top:0;
}
.accent {
background:#FFF;
text-align:center;
color:#3575B9;
}
.alert,.warning {
background:#FAFA8A;
color:#000;
}
.attention {
background:#D7E8F8;
color:#000;
}
.validate-attention {
background:#D7E8F8;
font-weight: bold;
color:#000;
}
.validate-attention ul {
margin: 0;
padding: 1em 2em;
}
.validate-attention ul li {
margin: 1em 0;
color:#000;
}
.alert h2,.attention h2 {
padding:.5em 0 0;
font-size:1.5em;
}
.alert h2:first-child, .attention h2:first-child {
padding-top: 0;
}
.attention h2 {
color:#F00;
}
.attention hr,.attention hr {
border:0;
border-top:1px solid #A7A9AC;
}
.critical {
background:#F7F7F7;
border:1px solid #91191C;
color:#91191C;
}
.shaded {
background:#EFEFEF;
color:#333;
}
.success {
background:#AAD868;
border:1px solid #9BCC60;
color:#2F440A;
}
.conspicuous {
overflow:hidden;
position:relative;
}
.conspicuous:before {
content:'';
display:block;
width:80%;
height:10px;
margin:0 auto;
border-radius:20em;
-webkit-box-shadow:0 0 10em #999;
box-shadow:0 0 10em #999;
position:absolute;
top:-10px;
left:10%;
z-index:90;
}
/* Header */
#header {
height:60px;
width:100%;
z-index:11;
position:absolute;
}
#header:after {
content:'';
width:50%;
height:64px;
position:absolute;
top:0;
left:50%;
z-index:-1;
}
.has_subnav:hover .svg_caret_up {
display:none;
}
.has_subnav:hover .svg_caret_down {
display:block;
}
.svg_caret_up, .svg_caret_down {
position:absolute;
top:-3px;
right:5px;
}
.svg_caret_down {
display:none;
}
#header .wrapper {
margin:0 10% auto;
z-index:1;
}
/* Header - Logo */
.logo {
padding:80px 19px;
height:22px;
width:131px;
}
.logo-svg {
position:relative;
top:20px;
float:left;
}
/* Header - Main Navigation */
#header .main_nav {
margin-left:74px;
position:relative;
}
#header .tab.tab_right {
float:right;
}
#header .tab {
float:left;
margin-left:30px;
text-align:center;
position:relative;
}
#header .tab.has_subnav {
padding-right:20px;
}
#header .tab a {
color:#163056;
display:inline-block;
width:100%;
text-decoration:none;
line-height:4;
}
#header .currency_tab a {
min-width:4em;
}
.caret {
border-left: 8px solid transparent;
border-right: 8px solid transparent;
border-top: 8px solid #000000;
width:1px;
}

#header.bg-dark .main_nav li.tab > a {
color:white;
}
.subnav .new a:after {
display:inline;
content:"NEW";
color:#fff;
background-color:#3575d3;
padding:4px 8px;
font-size:7px;
margin-left:.5rem;
line-height:8px;
border-radius:2px;
vertical-align:middle;
}
#header.bg-dark {
background:linear-gradient(0deg, rgba(9,107,145,0) 0%, rgba(18,40,73,0.4) 100%);
}
.svg-bg-dark {
fill:white;
}
#header .tab>a:hover {
border-bottom:2px solid #0D53B4;
}
#header .tab a:active {
color:#3575D3;
}
#header .tab img {
vertical-align:-0.1em;
}
#header .phone_tab {
float:left;
min-width:8em;
padding:0 0em 0 1.5em;
border:0;
line-height:4;
color:#FFF;
cursor:default;
}
.phone_tab span.gc-cs-link {
color:#fff;
}
.lt_ie9 #header .phone_tab {
min-width:4em;
}
#header .phone_tab span {
vertical-align:middle;
}
#header .phone_tab:hover,#header .live_chat_tab a:hover {
background:none;
}
#header .phone_tab:after {
content:'';
background:-webkit-gradient(linear,left top,right top,color-stop(0%,rgba(11,67,144,1)),color-stop(100%,rgba(14,83,177,0)));
background:-webkit-linear-gradient(left,rgba(11,67,144,1) 0%,rgba(14,83,177,0) 100%);
background:-moz-linear-gradient(left,rgba(11,67,144,1) 0%,rgba(14,83,177,0) 100%);
background:-ms-linear-gradient(left,rgba(11,67,144,1) 0%,rgba(14,83,177,0) 100%);
background:-o-linear-gradient(left,rgba(11,67,144,1) 0%,rgba(14,83,177,0) 100%);
background:linear-gradient(to right,rgba(11,67,144,1) 0%,rgba(14,83,177,0) 100%);
width:10px;
height:64px;
position:absolute;
top:0;
left:0;
}
#header .live_chat_tab {
float:right;
min-width:2em;
border-right:none;
}
#header .live_chat_tab .svg_icon {
display:block;
width:20px;
height:20px;
padding:0px;
}
#header .live_chat_tab path {
fill:#FFF;
}
@-webkit-keyframes chatbox   { 0%, 15%, 35%, 100% {opacity:0;} 16%, 34% {opacity:1;} }
   @-moz-keyframes chatbox   { 0%, 15%, 35%, 100% {opacity:0;} 16%, 34% {opacity:1;} }
     @-o-keyframes chatbox   { 0%, 15%, 35%, 100% {opacity:0;} 16%, 34% {opacity:1;} }
        @keyframes chatbox   { 0%, 15%, 35%, 100% {opacity:0;} 16%, 34% {opacity:1;} }
@-webkit-keyframes chatarrow { 0%, 15%, 35%, 100% {opacity:0;} 16%, 34% {opacity:1;} }
   @-moz-keyframes chatarrow { 0%, 15%, 35%, 100% {opacity:0;} 16%, 34% {opacity:1;} }
     @-o-keyframes chatarrow { 0%, 15%, 35%, 100% {opacity:0;} 16%, 34% {opacity:1;} }
        @keyframes chatarrow { 0%, 15%, 35%, 100% {opacity:0;} 16%, 34% {opacity:1;} }
#header .products_tab {
border-left:1px solid #0D53B4;
}
#header .login_tab {
border-right:0;
}
/* Header - Sub-navigation */
#header .tab.active .subnav,
#header .tab:hover .subnav {
display:block;
}
#header .subnav {
background-color:rgba(255,255,255);
border-radius:5px;
background-clip:padding-box;
display:none;
min-width:8em;
border:1px solid #F3F3F3;
border-top:0;
white-space:nowrap;
text-align:left;
-webkit-transition:all .1s ease-in 0;
-moz-transition:all .1s ease-in 0;
-o-transition:all .1s ease-in 0;
transition:all .1s ease-in 0;
position:absolute;
top:70px;
left:-1px;
box-shadow: 0 0.6rem 1.2rem rgba(0,0,0,0.175);
z-index: 1;
}
.subnav_padding {
width: 3em;
height: 10px;
}
#header .subnav li {
margin:0;
}
#header .subnav a {
padding:0 1em;
line-height:3;
color:#163056;
-webkit-transition:all .175s ease-in 0;
-moz-transition:all .175s ease-in 0;
-o-transition:all .175s ease-in 0;
transition:all .175s ease-in 0;
}
#header .subnav a:hover {
background:#F1F5FA;
background:rgba(241,245,250,.94);
color:#000;
border-top-left-radius:5px;
border-top-right-radius:5px;
}
#header .subnav a:active {
color:#C8C8C8;
}
/* Container */
#container.legacy {
width:960px;
margin:0 auto;
}
#container {
min-height:100%;
padding:4em 0 13em;
position:relative;
}
#container.bg_dark {
padding:0 0 13em;
}
.no_footer #container {
padding-bottom:0 !important;
}
#container:after {
box-shadow:inset 0 -3.05em 5.4em .9em #FFF;
}
/* Container - Cover */
.cover {
-webkit-background-size:cover;
-moz-background-size:cover;
-o-background-size:cover;
background-size:cover;
width:100%;
}
.cover_wrap {
width:960px;
height:100%;
margin:0 auto;
padding:1px 0;
position:relative;
}
.cover_wrap.padded {
padding:2em 0;
}
.cover_wrap.top_padded {
padding-top:2em;
}
.cover_wrap.bottom_padded {
padding-bottom:2em;
}
.hero {
background-color: #FFFFFF;
background-size: 1800px 520px;
background-position: center top;
background-repeat: no-repeat;
}
.hero .cover_wrap {
padding:3.5em 32.5em 0 1em;
}
.hero.hero-left .cover_wrap {
text-align: left;
}
.hero.hero-center .cover_wrap {
text-align: center;
}
.hero.hero-center ul {
padding: 0 20px;
}
.hero.hero-right .cover_wrap {
text-align: right;
}
.hero.hero-right ul {
padding: 0 40px 0 0;
}
.hero.hero-gutter .cover_wrap {
text-align: right;
}
.hero.hero-gutter li b {
display:inline-block;
width:8em;
text-align:left;
}
.hero .cover_wrap .price-bottom {
margin-bottom: .5em;
}
.skinny-bar {
background-color:#3676B8;
background:-webkit-gradient(linear,left bottom,right bottom,color-stop(0%,#0D53B4),color-stop(100%,#3575B9));
background:-webkit-linear-gradient(left,#0D53B4 0%,#3575B9 100%);
background:-moz-linear-gradient(left,#0D53B4 0%,#3575B9 100%);
background:linear-gradient(to right,#0D53B4 0%,#3575B9 100%);
color:#fff;
text-align:center;
position:relative;
height: 65px;
line-height: 65px;
display:block;
text-transform:lowercase;
-webkit-font-smoothing: antialiased;
color:#fff;
}
.product-upsell.shadow-top,
.skinny-bar.shadow-top {
-webkit-box-shadow: inset 0px 4px 9px rgba(0, 0, 0, 0.6);
box-shadow: inset 0px 4px 9px rgba(0, 0, 0, 0.6);
}
.skinny-bar.shadow-top.shadow-bot {
-webkit-box-shadow: inset 0px 4px 9px rgba(0, 0, 0, 0.6);
box-shadow: inset 0px 4px 9px rgba(0, 0, 0, 0.6);
}
.skinny-bar-large-text {
font-size:1.7em;
line-height: 65px;
margin: 0;
display: inline-block;
vertical-align: top;
}
.skinny-bar-small-text {
font-size: 1.1em;
line-height: 65px;
margin: 0;
}
.skinny-bar .btn_secondary,
.skinny-bar-small-text,
.skinny-bar .phone,
.skinny-bar .svg_icon,
.skinny-bar a {
margin-left:48px;
display: inline-block;
vertical-align: top;
}
.skinny-bar .btn_secondary {
margin: 16px 0 0 48px;
}
.skinny-bar:hover {
text-decoration:none;
}
.skinny-bar .phone {
font-size:2.2em;
}
.phone_link:link, .phone_link:visited {
text-decoration:none;
color:inherit;
}
.skinny-bar .svg_icon {
width: 40px;
height: 40px;
position: relative;
top: 11px;
}
.skinny-bar a.skinny-bar-imgbtn {
margin-left:24px;
}
.skinny-bar img {
vertical-align:middle;
}
.skinny-bar .svg_icon g * {
fill: #fff;
}
.skinny-bar-has-tab-nav {
padding-top: 25px;
height: 90px;
}
.nav-on-skinny-bar {
position: relative;
top: 35px;
z-index: 5;
}
.cover_shadow:after {
content:"";
position:absolute;
top:0;
left:0;
right:50.05%;
background:-moz-linear-gradient(top, rgba(0,0,0,0.35) 0%, rgba(0,0,0,0) 40%, rgba(0,0,0,0) 100%);
background:-webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.35)), color-stop(40%,rgba(0,0,0,0)), color-stop(100%,rgba(0,0,0,0)));
background:-webkit-linear-gradient(top, rgba(0,0,0,0.35) 0%,rgba(0,0,0,0) 40%,rgba(0,0,0,0) 100%);
background:linear-gradient(to bottom, rgba(0,0,0,0.35) 0%,rgba(0,0,0,0) 40%,rgba(0,0,0,0) 100%);
height:130px;
}
/* Footer */
#footer {
font-family:'Open Sans';
font-weight:300;
background:#3575D3;
width:100%;
min-height:17em;
margin:-9em auto 0;
position:absolute;
z-index:10;
left: 50%;
transform:translateX(-50%);
}
/* Footer - Footer Navigation */
.footer_nav,.footer_nav ul {
min-height:12em;
}
.footer_nav {
width: 70%;
max-width:1008px;
float:left;
height:415px;
background:#5588D2;
padding-left:10em;
padding-top:40px;
}
.footer_nav ul {
float:left;
margin:0;
line-height:2;
}
.footer_nav .link_columns {
width:25%;
}
.ie7 .footer_nav .products_list {
width:10em;
}
.ie7 .footer_nav .programs_list {
width:8em;
}
.ie7 .footer_nav .support_list {
width:8em;
}
.ie7 .footer_nav .company_list {
width:17.71em;
}
.footer_nav .title,.footer_nav a {
color:#FFF;
}
.footer_nav .title {
display:block;
float:none;
margin:0;
padding:0;
font-size:1.25em;
font-weight: bolder;
}
.footer_nav h3 {
margin:1em 0 .5em;
font-size:16px;
}
.footer_nav li {
float:left;
min-width:9.5em;
padding:0 1em 0 0;
white-space:nowrap;
font-size:14px;
}
.lt_ie9 .footer_nav li {
min-width:7.5em;
}
.footer_nav a {
text-decoration:none;
}
.footer_nav a:hover {
text-decoration:underline;
}
/* Footer - Social Media */
#footer .social_media {
height:3.5em;
float: left;
width: 30%;
max-width:432px;
height: 350px;
}
#footer .social_media .wrapper {
height:100%;
margin-left:7rem;
padding:0 .8em;
position:relative;
}
#footer .social_icon {
margin:1.3em .8em 0 0;
}
#footer .social_container {
text-align:left;
}
#footer .brand_logo {
margin-top:4em;
text-align:left;
}
#footer .disclaimers {
background-color:#F9f9f9;
padding:1em 0em;
}
#footer .disclaimers .container {
margin-left: 15%;
}
#footer .brand_name {
font-size:1.8em;
text-align:right;
color:#FFF;
}
#footer .phone {
text-align:left;
font-size:1.2em;
color:#FFF;
line-height: 3em;
}
#footer .social_icon,#footer .social_icon .svg_icon {
width:22px;
height:22px;
}
#footer .social_icon path,#footer .social_icon polygon,#footer .social_icon rect {
fill:#FFF;
}
#footer .social_icon:hover path,#footer .social_icon:hover polygon,#footer .social_icon:hover rect {
fill:#D8EBFF;
}
.copyright {
text-align:left;
font-size:.7em;
line-height:1.5em;
color:#FFF;
}
.promotional_price_footer {
width:33em;
}
.copyright .footer_promo {
margin:0;
}
.copyright a,.copyright a:hover {
text-decoration:underline;
color:#FFF;
}
.copyright.raised {
top:1.2em;
}
#analytics,
#trackr {
bottom:0;
left:-10000px;
position:absolute;
z-index:-1;
}
/* Recaptcha fixes */
iframe[src="about:blank"] {
display:none;
}
.recaptchatable a {
text-transform:none;
}
/* Core Widgets */
/* Core Widgets - Features */
.features ul,.features li {
padding-left:0;
list-style:none;
}
.features li {
background-repeat:no-repeat;
background-position:top center;
float:left;
width:25%;
min-height:21em;
text-align:center;
}
.features a {
display:block;
color:#666;
}
.features h4 {
margin:.4em 0 1em;
font-size:1.4em;
}
.features p {
min-height:8.8em;
margin:.6em 0;
padding:0 1.8em;
line-height:1.4em;
}
/* SVG Icons */
.svg_icon {
display:inline-block;
}
.svg_icon object,
.svg_icon svg {
display:block;
}
.iconbtn:hover .svg_icon:not(.no_hover) .filled,
.iconbtn:hover .svg_icon:not(.no_hover) .outlined,
.svg_icon:not(.no_hover) .filled,
.svg_icon:not(.no_hover):hover .outlined,
button:hover .svg_icon:not(.no_hover) .outlined,
a:hover .svg_icon:not(.no_hover) .outlined {
display:none;
}
.iconbtn:hover .svg_icon:not(.no_hover) .filled,
.svg_icon:not(.no_hover):hover .filled,
button:hover .svg_icon:not(.no_hover) .filled,
a:hover .svg_icon:not(.no_hover) .filled {
display:inline;
}
/* Overlay, Lightbox */
.unlimited,
.moneyback,
.freedomain {
text-decoration:underline;
cursor:pointer;
}
.price_display_main .moneyback,
.price_display_main .unlimited,
.initial_display .moneyback,
.initial_display .unlimited {
color:#666;
}
.price_display_main .moneyback:hover,
.price_display_main .unlimited:hover,
.initial_display .moneyback:hover,
.initial_display .unlimited:hover {
color:#3575B9;
}
.lightbox_bg {
background:#FFF;
-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
filter:alpha(opacity=80);
opacity:.8;
position:fixed;
top:0;
right:0;
bottom:0;
left:0;
z-index:99999;
}
.lightbox_bg .lightbox_loading {
width:50em;
height:2em;
margin-top:-1em;
margin-left:-25em;
text-align:center;
font-weight:bold;
font-size:1.2em;
line-height:2em;
position:absolute;
top:50%;
left:50%;
}
.lightbox_container {
background:#FFF;
padding:0;
border:1px solid #3676B8;
border-radius:5px;
text-align:left;
position:fixed;
top:50%;
left:50%;
z-index:999999;
}
.lightbox_container .title {
background:#3676B8;
height:50px;
margin:0;
padding:0 40px;
line-height:50px;
color:#FFF;
}
.lightbox_container .close {
padding:0;
cursor:pointer;
background:transparent;
border:0;
line-height:0;
-webkit-appearance:none;
position:absolute;
top:12.5px;
right:12.5px;
}
.lightbox_container .close .svg_icon {
width:25px;
height:25px;
}
.lightbox_container .close .svg_icon path {
fill:#FFF;
}
.lightbox_container .close:active .svg_icon path {
fill:#AAA;
}
.lightbox_container .close:focus {
outline:0;
}
.lightbox_container div.lightbox_padding {
width:100%;
overflow:auto;
}
.lightbox_container div.lightbox_padding:focus {
outline:0;
}
.lightbox_container iframe.lightbox_padding {
margin-bottom:-5px;
border:0;
}
.lightbox_container .lightbox_content h1,.lightbox_container .lightbox_content h2,.lightbox_container .lightbox_content h3,.lightbox_container .lightbox_content h4,.lightbox_container .lightbox_content h5,.lightbox_container .lightbox_content h6 {
margin-top:2em;
text-transform:none;
font-weight:700;
font-size:1em;
color:inherit;
}
.lightbox_container .lightbox_content {
margin:40px;
}
.lt_ie9 .lightbox_container .lightbox_content {
padding-bottom:40px;
}
/* Padding for any sections that aren't jammed together */
section.padded_top {
margin-top:3em;
}
section.padded_top_light {
margin-top: 1.5em;
}
section.padded_top_light .subheading {
margin-bottom: .5em;
}
section.padded_bottom {
margin-bottom:3em;
}
section.padded {
margin:3em auto;
}
/* Hero slider */
.slider {
height:30em;
position:relative;
}
#content .slider .slide {
position:absolute;
width:100%;
opacity:0;
transition:all 0.5s ease;
}
#content .slider .slide.active {
opacity:1;
}
.slider .slide .hookline,
.slider .slide .heading,
.slider .slide h1,
.slider .slide h2,
.slider .slide ul,
.slider .slide #p_tagline {
position:relative;
left:-25px;
opacity:0;
transition:all 0.5s ease;
}
.slider .slide.active .hookline,
.slider .slide.active .heading,
.slider .slide.active h1,
.slider .slide.active h2,
.slider .slide.active ul,
.slider .slide.active #p_tagline {
left:0;
opacity:1;
}
.slider .controls {
position:absolute;
left:50%;
margin-left:-480px;
}
.no_js .slider .controls {
display:none;
}
.slider .controls a {
display:block;
position:absolute;
top:13em;
z-index:10;
width:64px;
height:64px;
}
.controls-btn-circle:hover,
.controls-btn-circle {
background:transparent;
border:0;
border-radius: 50px;
margin: 55px 10px 0 10px;
}
.slider .svg_icon {
width:64px;
height:64px;
cursor:pointer;
}
.slider [data-slide=left] {
left:0;
}
.slider [data-slide=right] {
right:0;
}
/* ==========
    Products
   ========== */
.page-pickplan h1 {
margin-bottom: 0;
}
.products-wrapper {
padding:48px 0;
margin:0 auto;
width: 1200px;
position: relative;
z-index:0;
left: -113px;
}
.products-wrapper.col-2 .product-labels {
width:200px;
}
.products-wrapper.col-2 .product {
width: 350px;
}
.products-wrapper.col-3 .product-labels {
width: 200px;
}
.products-wrapper.col-3 .product {
width: 225px;
}
.products-wrapper.col-4 {
left:0;
}
.products-wrapper.col-4 .product-labels {
width: 130px;
}
.products-wrapper.col-4 .product {
width: 180px;
}
.product-labels {
padding:213px 0 0;
margin:0 10px 30px;
float:left;
display:block;
}
.product {
padding:0;
margin:0 10px 30px;
display:block;
float:left;
text-decoration:none;
color:#666;
border-radius: 5px;
background-clip: padding-box;
z-index:1;
position:relative;
font-weight:normal;
}
button.product::-moz-focus-inner {
padding: 0;
border: 0
}
#content button.product {
border:0;
background:transparent;
font:lighter 16px 'Open Sans','Segoe UI','Gill Sans','Gill Sans MT',GillSans,'Trebuchet MS',Arial,sans-serif;
margin-bottom:0;
height:auto;
cursor:pointer;
width: 248px;
}
.product.default {
background: #FFF;
}
.product:hover {
text-decoration:none;
}
.product .offer {
display: block;
padding: 10px;
background:#F8F8F8;
border:1px solid #ccc;
border-bottom:0;
border-radius: 5px 5px 0 0;
height:80px;
}
.product.sale-tagline .offer {
border-radius:0;
border-top:0;
}
.product .offer-top {
display: block;
text-align: center;
}
.product .name {
font-size: 26px;
display: block;
text-align: center;
text-transform: lowercase;
background: #FFF;
padding-bottom: 8px;
padding-top: 34px;
height:74px;
font-weight:300;
}
.product.sale-tagline .name {
padding-top:0;
height:40px;
}
.product.sale-tagline .sale-tagline {
height: 34px;
position: relative;
background: #f99c08;
color: #fff;
display: block;
font-size: 20px;
text-transform: lowercase;
text-align: center;
line-height: 34px;
border-radius: 5px 5px 0 0;
}
.product .price {
position: relative;
}
.product.coming-soon .price {
font-size: 20px;
text-align: center;
display: block;
padding: 16px;
}
.product .current-price {
text-align: right;
position: relative;
display: block;
width: auto;
}
.product .priced-at {
line-height: 40px;
font-size: 35px;
font-weight: 300;
}
.products-wrapper.col-4 .product .priced-at {
font-size: 35px;
display: inline-block;
float: left;
}
.products-wrapper.col-3 .product .priced-at {
display: inline-block;
float: left;
text-align: center;
padding-left: 20%;
}
.product:hover .priced-at {
color: #3575d3;
}
.product .period {
text-align: left;
line-height: 1em;
display: inline;
float: left;
position: relative;
margin: 0;
width: 30px;
padding: 5px;
}
.product:hover .btn,
.product .btn {
font:lighter 16px 'Open Sans','Segoe UI','Gill Sans','Gill Sans MT',GillSans,'Trebuchet MS',Arial,sans-serif;
border-radius: 0;
border: none;
display: block;
line-height: 32px;
background: #62bc33;
background: -moz-linear-gradient(left, #62bc33 0%, #8bd331 100%);
background: -webkit-gradient(linear, left top, right top, color-stop(0%,#62bc33), color-stop(100%,#8bd331));
background: -webkit-linear-gradient(left, #62bc33 0%,#8bd331 100%);
background: -o-linear-gradient(left, #62bc33 0%,#8bd331 100%);
background: -ms-linear-gradient(left, #62bc33 0%,#8bd331 100%);
background: linear-gradient(to right, #62bc33 0%,#8bd331 100%);
color: #fff;
font-size: 24px;
height:46px;
}
.product.coming-soon:hover .btn,
.product.coming-soon .btn,
.product.coming-soon.active .btn,
.product.coming-soon:active .btn,
.product.coming-soon:focus .btn {
background:#ccc;
color:#999;
}
.product:hover .btn {
background:#8bd331;
color:#FFF;
}
.product:active .btn,
.product.active .btn {
background:#62bc33;
color:#FFF;
}
#content .product button {
font-size: 24px;
margin-bottom: 0;
display: block;
width: 100%;
}
#content .product button.btn-bottom,
.product:hover .btn-bottom,
.product .btn-bottom {
border-radius: 0 0 5px 5px;
line-height: 20px;
height: 32px;
font-size: 20px;
}
.product .description-wrapper {
padding: 12px 10px;
border:1px solid #ccc;
background:#f8f8f8;
display:block;
}
.product.default .offer,
.product.default .description-wrapper {
background:#FFF;
}
.product.default.coming-soon .offer {
background:inherit;
}
.product-labels .description-wrapper .specs:last-child,
.product .description-wrapper .specs:last-child {
border-bottom:none;
}
.product-labels .specs,
.product .specs {
border-bottom: 1px #ddd solid;
padding: 4px 0;
margin: 0;
display:block;
}
.product-labels .specs {
border-color:transparent;
}

.js-unlimited {
cursor: help;
}

.product .spec-item {
line-height: 30px;
text-align: center;
overflow:hidden;
margin: 0 -10px;
height:30px;
position: relative;
display:block;
}
.product-labels .spec-item.free-ssl {
text-transform: none;
}
.product .spec-item.hover:before,
.product .spec-item:hover:before {
position:absolute;
content: "";
display:block;
height:30px;
background:rgba(0,0,0,.1);
width:100%;
z-index:2;
left:-10px;
}
.col-1 .product .spec-item.hover:before,
.col-1 .product .spec-item:hover:before {
left:0;
}
.products-wrapper.col-4 .product .spec-item.hover:before,
.products-wrapper.col-4 .product .spec-item:hover:before {
left:0;
}
.ie8 .product .spec-item.hover:before,
.ie8 .product .spec-item:hover:before {
background: transparent;
border-bottom:#ddd;
}
.products-wrapper.col-2 .product .spec-item.hover:before,
.products-wrapper.col-2 .product .spec-item:hover:before {
width:360px;
}
.products-wrapper.col-3 .product .spec-item.hover:before,
.products-wrapper.col-3 .product .spec-item:hover:before {
width:240px;
}
.products-wrapper .product .spec-label {
position:absolute;
top:40px;
}
.product-labels .spec-item {
list-style:none;
padding:0;
margin:0;
text-align:right;
line-height:30px;
height:30px;
text-transform:lowercase;
}
.product-labels .spec-item.hover .spec-label,
.product-labels .spec-item:hover .spec-label {
color:#3575d3;
font-weight:600;
}
.product-labels .spec-item.hover:before,
.product-labels .spec-item:hover:before {
content: '';
display: block;
width: 720px;
background: rgba(0,0,0,.1);
left:230px;
height: 30px;
position: absolute;
padding: 0;
}
.products-wrapper.col-3 .product-labels .spec-item.hover:before,
.products-wrapper.col-3 .product-labels .spec-item:hover:before {
width:715px;
left: 230px;
}
.products-wrapper.col-4 .product-labels .spec-item.hover:before,
.products-wrapper.col-4 .product-labels .spec-item:hover:before {
width:780px;
left: 160px;
}
.product .spec-value {
font-weight:600;
}
.product .blue-bar .spec-value,
.product .grey-bar .spec-value {
background: #bbb;
display: block;
margin: 3px 20px;
border-radius: 18px;
color: #FFF;
line-height: 24px;
height: 24px;
padding: 0 5px 0 5px;
}
.product .blue-bar .spec-value {
background:#3575B9;
}
.products-wrapper.col-2 .specs-extras .spec-item:hover:before,
.products-wrapper.col-2 .specs-extras .spec-item.hover:before,
.product .blue-bar.hover:before,
.product .grey-bar.hover:before,
.product:hover .blue-bar:before,
.product:hover .grey-bar:before,
.specs-extras .spec-label {
display:none;
}
.text-light p,
.text-light h3,
.text-light {
color:#FFF;
}
.text-dark p,
.text-dark h3,
.text-dark {
color:#444;
}
@media print {
    * {
        * background: transparent !important;
        * color: #000 !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    a, a:visited { text-decoration: underline; }
    a[href]:after { content: " (" attr(href) ")"; }
    abbr[title]:after { content: " (" attr(title) ")"; }
    a[href^="javascript:"]:after, a[href^="#"]:after { content: ""; }
    pre, blockquote { border: 1px solid #999; page-break-inside: avoid; }
    thead { display: table-header-group; }
    tr, img { page-break-inside: avoid; }
    img { max-width: 100% !important; }
    p, h2, h3 { orphans: 3; widows: 3; }
    h2, h3 { page-break-after: avoid; }
    #header, #footer .footer_nav, #footer .social_media a, .content-link, #nav, .promotional_price_footer, .terms_conditions .tos_list { display: none !important; }
    .terms_content, .terms_content *, #footer .copyright { font-family:serif !important;}
}
.affiliate_disclosure_indent {
margin-left:3em;
}
.terms_conditions .affiliate_disclosure_indent p {
font-size:11px;
}
.load_english {
float:right;
}
#header .phone_tab.phone_right {
float:right;
}
.hamburger-svg {
    display: none;
}
#header .phone_tab.phone_right:after {
left:-39.4em;
}
#header .extra-mobile-logo {
    display:none;
}
#shared-pro #pro-package .btn_primary {
width:52%;
left:30%;
}
#shared-pro #pro-package {
background:#FFF url(/media/shared/signup/pro-servers.png) no-repeat 29em;
height:215px;
margin:0;
padding:2em 1.5em;
border:1px solid #4e535a;
border-radius:5px;
position:relative;
}
#pro-package {
background:#FFF url(/media/shared/signup/pro-servers.png) no-repeat 29em;
height:215px;
margin:48em 0 -1em 6em;
padding:2em 1.5em;
border:1px solid #4e535a;
border-radius:5px;
position:relative;
}
#pro-package h2 {
display:inline-block;
margin:0;
color:#282d33;
border-bottom:7px solid #00BB0E;
text-transform:none;
font-weight:bold;
font-size:1.75em;
}
#pro-package .normal-rate {
font-size:1.1em;
color:#b8b8b8;
position:relative;
top:.5em;
}
#pro-package .sale-rate {
margin:.1em 0;
font-size:2em;
}
#pro-package .sale-rate .sale-text {
font-size:1.5em;
margin:0;
}
#pro-package .sale-rate .notice {
font-size:1.6em;
}


#pro-package .sale-rate .text-mod {
padding:0 0 0 .15em;
vertical-align:super;
font-size:.5em;
}
#pro-package .sale-rate .text-term {
font-size: .5em;
font-weight: bold;
}
#pro-package .tagline {
width:19em;
margin:0;
font-size:.85em;
}
#pro-package .column-wrap {
float:left;
margin:0 1em .1em 0;
}
#pro-package ul,#pro-package li {
margin:.5em;
padding:0 1em;
list-style:none;
font-size: 16px;
}
#pro-package ul {
float:left;
}
#pro-package li {
margin:0 0 1.1em;
padding:0 0 0 1.5em;
position:relative;
width:165px;
}
#pro-package li i:before {
margin:-.5em 0 0;
font-size:2em;
position:absolute;
top:50%;
left:0;
}
#pro-package .btn-wrapper {
float:right;
width:22em;
height:100%;
text-align:center;
position:absolute;
top:0;
right:0;
}
#pro-package .btn_primary {
margin:-1em 0 0 -2.2em;
font-size:1.75em;
position:absolute;
top:50%;
left:50%;
width:52%;
}
form > #pro-package {
margin-top:43em;
}
#pro-package a {
margin: 3em 0 0 7em;
height: 50px;
}
.timeline {
list-style-type: none;
display: flex;
align-items: center;
margin-left: -12%;
}
.li {
width: 13%;
}
.timestamp {
margin-bottom: 20px;
padding: 0px 40px;
display: flex;
flex-direction: column;
align-items: center;
font-weight: 100;
}
.status {
border-top: 2px solid #ced7df;
position: relative;
}
.status:before {
content: "";
width: 18px;
height: 18px;
background-color: #f6f8f9;
border-radius: 25px;
border: 1px solid #ddd;
position: relative;
top: -11px;
float: right;
}
.li.complete .status {
border-top: 2px solid #3575D3;
}
.li.complete .status:before {
background-color: #3575D3;
border: none;
}
.li.complete .status h6 {
color: #666;
font-weight: bold;
text-transform: capitalize;
}
.bar {
width: 32%;
}
.complete-step {
border-top: 2px solid #3575D3;
position: relative;
}
.incomplete-step {
border-top: 2px solid #ced7df;
position: relative;
}
.no-status .step-name>h6 {
text-indent: 100%;
white-space: nowrap;
overflow: hidden;
}
.pagination-container {
padding-top: 1%;
}
.step-name {
float: right;
margin-right: -17%;
}
.timeline h6 {
color: #666;
text-transform: capitalize;
width: 100%;
white-space: nowrap;
overflow: hidden;
text-overflow: ellipsis;
}
.pagination-gradient {
height: 2.9%;
width: 100%;
position: absolute;
top: 4%;
background-color: #f6f8f9;
left: 0%;
}
@media (min-device-width: 320px) and (max-device-width: 1085px) {
.bar {
width: 29%;
}
.pagination-gradient {
display: none;
}
}
.lightbox_container .lightbox_content.seotools {
margin:0px;
}
.seotools .hero_text_container {
position:relative;
height:180px;
background-repeat:no-repeat;
background-size:cover;
}
.seotools .hero_text {
color:#87C0E5;
padding:2em 0 1em 1.3em;
font-size:1.5em;
width:300px;
}
.seotools .hero_text b {
color:#0087D0;
display:block;
font-size:0.8em;
margin-top:20px;
}
.seotools .seotools_mission {
display:flex;
align-items:center;
justify-content:space-between;
margin:1em 2em;
}
.nexus_category {
max-width: 350px;
font-size: 17px;
}
.seotools .page_divider {
height:1px;
border-bottom:1px solid #E7E8E7;
margin:0 2em 1em 2em;
}
.seotools .seotools_question {
display:flex;
align-items:center;
justify-content:center;
color:#2299D6;
font-weight:bold;
}
.seotools .reason_list {
margin:0 15%;
padding:0 0 1em 0;
transform:scale(0.9);
list-style:none;
}
.seotools .reason_list div {
display: inline-block;
position: relative;
top: -7px;
margin: 1em;
width: 350px;
}
.seotools .reason_list img {
width:55px;
}
.seotools .seotools_footer {
display:flex;
flex-direction:column;
background-color:#3676B8;
align-items:center;
justify-content:center;
color:#FFF;
padding:1em;
}
.sitelock_fix tbody {
vertical-align:top;
}
.sitelock_fix th, .sitelock_fix tr, .sitelock_fix td {
border-right:0;
pointer-events:none;
}
